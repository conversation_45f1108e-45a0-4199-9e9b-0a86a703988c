import { GoogleGenAI, Type, GenerateContentResponse, GenerateContentParameters } from "@google/genai";
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile } from '@ffmpeg/util';
import { WordOccurrence, GeminiModelQuality } from '../types';

class GeminiService {
  private ai: GoogleGenAI;
  private ffmpeg: FFmpeg | null = null;

  constructor() {
    if (!process.env.API_KEY) {
      throw new Error("API_KEY environment variable not set. The application cannot initialize the Gemini service.");
    }
    this.ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
  }

  private async loadFFmpeg(onProgress: (progress: number) => void): Promise<FFmpeg> {
    if (this.ffmpeg && this.ffmpeg.loaded) {
        onProgress(10); // Already loaded, just indicate progress
        return this.ffmpeg;
    }
    
    const ffmpeg = new FFmpeg();
    ffmpeg.on('log', ({ message }) => console.log('[ffmpeg]', message));
    
    onProgress(5);
    await ffmpeg.load({
        coreURL: 'https://cdn.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/esm/ffmpeg-core.js',
        wasmURL: 'https://cdn.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/esm/ffmpeg-core.wasm',
    });
    onProgress(10);
    
    this.ffmpeg = ffmpeg;
    return this.ffmpeg;
  }

  private uint8ArrayToBase64(bytes: Uint8Array): string {
    let binary = '';
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return (window as any).btoa(binary);
  }

  private async processAudioChunk(
    audioPart: { inlineData: { mimeType: string; data: string } },
    wordsToFind: string[],
    quality: GeminiModelQuality
  ): Promise<{ word: string; startTime: number; endTime: number }[]> {
      const schema = {
        type: Type.ARRAY,
        description: "A list of all occurrences of the specified words found in the video.",
        items: {
          type: Type.OBJECT,
          properties: {
            word: { type: Type.STRING, description: 'The censored word detected.' },
            startTime: { type: Type.NUMBER, description: 'The timestamp in seconds when the word starts.' },
            endTime: { type: Type.NUMBER, description: 'The timestamp in seconds when the word ends.' },
          },
          required: ['word', 'startTime', 'endTime'],
        },
      };

      const prompt = `You are a precise audio analysis tool. Analyze the provided audio file. Identify every occurrence of the following case-insensitive words: "${wordsToFind.join('", "')}". Return a JSON array of objects with "word", "startTime", and "endTime". If no words are found, return an empty array. Be extremely accurate with timestamps.`;

      const modelConfig: GenerateContentParameters['config'] = {
          responseMimeType: 'application/json',
          responseSchema: schema,
      };

      if (quality === 'flash') {
          modelConfig.thinkingConfig = { thinkingBudget: 0 };
      }

      const response = await this.ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: { parts: [audioPart, { text: prompt }] },
        config: modelConfig,
      });

      const responseText = response.text.trim();
      return responseText ? JSON.parse(responseText) : [];
  }

  async detectWords(
    videoFile: File,
    videoDuration: number,
    badWords: string,
    onProgress: (progress: number) => void,
    quality: GeminiModelQuality
  ): Promise<WordOccurrence[]> {
    console.log(`Starting detection for: ${videoFile.name}, Duration: ${videoDuration}s`);
    const wordsToFind = badWords.toLowerCase().split(',').map(w => w.trim()).filter(Boolean);
    if (wordsToFind.length === 0) return [];

    try {
        const ffmpeg = await this.loadFFmpeg(onProgress);

        onProgress(15);
        await ffmpeg.writeFile('input.vid', await fetchFile(videoFile));

        onProgress(20);
        console.log('Extracting audio...');
        await ffmpeg.exec(['-i', 'input.vid', '-vn', '-acodec', 'libmp3lame', '-q:a', '5', 'audio.mp3']);
        onProgress(30);
        
        const CHUNK_DURATION_S = 300; // 5 minutes
        const numChunks = Math.ceil(videoDuration / CHUNK_DURATION_S);
        let allOccurrences: WordOccurrence[] = [];

        console.log(`Splitting audio into ${numChunks} chunk(s).`);

        for (let i = 0; i < numChunks; i++) {
            const chunkStartTime = i * CHUNK_DURATION_S;
            const chunkName = `chunk-${i}.mp3`;
            const progressBase = 30;
            const progressPerChunk = 60 / numChunks;

            console.log(`Processing chunk ${i + 1}/${numChunks}...`);
            await ffmpeg.exec(['-i', 'audio.mp3', '-ss', chunkStartTime.toString(), '-t', CHUNK_DURATION_S.toString(), '-c', 'copy', chunkName]);

            const chunkData = await ffmpeg.readFile(chunkName) as Uint8Array;
            const chunkBase64 = this.uint8ArrayToBase64(chunkData);
            const audioPart = { inlineData: { mimeType: 'audio/mp3', data: chunkBase64 } };

            const chunkOccurrences = await this.processAudioChunk(audioPart, wordsToFind, quality);
            
            const adjustedOccurrences = chunkOccurrences.map((item, index) => ({
                ...item,
                word: item.word.toLowerCase(),
                id: `${item.word}-${item.startTime.toFixed(2)}-${Date.now()}-${index}`,
                status: 'pending' as const,
                startTime: item.startTime + chunkStartTime,
                endTime: item.endTime + chunkStartTime,
            }));
            
            allOccurrences.push(...adjustedOccurrences);
            onProgress(progressBase + ((i + 1) * progressPerChunk));
        }

        allOccurrences.sort((a, b) => a.startTime - b.startTime);
        onProgress(100);
        console.log('Detection complete.', allOccurrences);
        return allOccurrences;

    } catch (error) {
        console.error("Error during ffmpeg/gemini processing:", error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
        throw new Error(`Processing Error: ${errorMessage}`);
    }
  }

  async exportVideo(
    videoFile: File,
    videoDuration: number,
    acceptedOccurrences: WordOccurrence[],
    onProgress: (progress: number) => void
  ): Promise<string> {
    console.log(`Starting export for: ${videoFile.name}`);
    if (acceptedOccurrences.length === 0) {
      console.warn("Export called with no accepted occurrences.");
      throw new Error("No words were accepted for muting.");
    }

    try {
      const ffmpeg = await this.loadFFmpeg(p => onProgress(p));
      
      const progressCallback = ({ time }: { time: number }) => {
        const progress = (time / videoDuration) * 100;
        onProgress(10 + (progress * 0.88));
      };

      ffmpeg.on('progress', progressCallback);

      onProgress(10);
      await ffmpeg.writeFile('input.vid', await fetchFile(videoFile));
      onProgress(12);

      const volumeFilters = acceptedOccurrences
        .map(occ => `volume=enable='between(t,${occ.startTime.toFixed(3)},${occ.endTime.toFixed(3)})':volume=0`)
        .join(',');
      
      const filterComplex = `[0:a]${volumeFilters}[a]`;
      
      const outputFilename = `IntelliMute_${videoFile.name}`;

      const command = [
        '-i', 'input.vid',
        '-filter_complex', filterComplex,
        '-map', '0:v',
        '-map', '[a]',
        '-c:v', 'copy',
        outputFilename
      ];

      console.log('Executing FFmpeg command:', command.join(' '));
      await ffmpeg.exec(command);
      onProgress(99);

      const data = await ffmpeg.readFile(outputFilename) as Uint8Array;
      const blob = new Blob([data.buffer], { type: 'video/mp4' });
      const url = URL.createObjectURL(blob);
      
      // Clean up fs
      await ffmpeg.deleteFile(outputFilename);
      await ffmpeg.deleteFile('input.vid');

      onProgress(100);
      console.log('Export complete and returning object URL.');
      
      ffmpeg.off('progress', progressCallback);
      
      return url;

    } catch (error) {
        console.error("Error during FFmpeg export:", error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
        throw new Error(`Export Error: ${errorMessage}`);
    }
  }
}

export const geminiService = new GeminiService();