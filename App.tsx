



import React, { createContext, useReducer, Dispatch, useMemo, useEffect, useRef } from 'react';
import { AppState, Action, WordOccurrence } from './types';
import LeftSidebar from './components/LeftSidebar';
import Timeline from './components/Timeline';
import RightInspector from './components/RightInspector';
import Header from './components/Header';
import VideoDropzone from './components/VideoDropzone';
import ProcessingOverlay from './components/ProcessingOverlay';
import VerificationScreen from './components/VerificationScreen';

const initialState: AppState = {
  videoFile: null,
  videoUrl: null,
  videoDuration: 0,
  badWords: 'heck,darn,fudge',
  occurrences: [],
  selectedOccurrenceId: null,
  currentVideoTime: 0,
  isPlaying: false,
  processingState: 'idle',
  processingProgress: 0,
  errorMessage: null,
  analysisQuality: 'pro',
  exportUrl: null,
  timelineView: { start: 0, end: 0 },
  hasJustUploaded: false,
};

function appReducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'SET_VIDEO':
      return { 
        ...initialState, 
        ...action.payload, 
        badWords: state.badWords, 
        analysisQuality: state.analysisQuality,
        timelineView: { start: 0, end: action.payload.duration },
        hasJustUploaded: true,
      };
    case 'RESET_VIDEO':
      if (state.exportUrl) URL.revokeObjectURL(state.exportUrl);
      return {...initialState, badWords: state.badWords, analysisQuality: state.analysisQuality};
    case 'SET_BAD_WORDS':
      return { ...state, badWords: action.payload, hasJustUploaded: false };
    case 'SET_ANALYSIS_QUALITY':
      return { ...state, analysisQuality: action.payload };
    case 'START_DETECTION':
      return { ...state, processingState: 'detecting', processingProgress: 0, occurrences: [], selectedOccurrenceId: null, hasJustUploaded: false };
    case 'DETECTION_PROGRESS':
      return { ...state, processingProgress: action.payload };
    case 'DETECTION_COMPLETE':
      return { ...state, processingState: 'idle', occurrences: action.payload, processingProgress: 100 };
    case 'DETECTION_ERROR':
        return { ...state, processingState: 'error', errorMessage: action.payload };
    case 'SELECT_OCCURRENCE':
      return { ...state, selectedOccurrenceId: action.payload.id, currentVideoTime: action.payload.time, isPlaying: false };
    case 'UPDATE_OCCURRENCE_STATUS':
      return {
        ...state,
        occurrences: state.occurrences.map(occ =>
          occ.id === action.payload.id ? { ...occ, status: action.payload.status } : occ
        ),
      };
    case 'UPDATE_OCCURRENCE_TIME':
      return {
        ...state,
        occurrences: state.occurrences.map(occ =>
          occ.id === action.payload.id ? { ...occ, startTime: action.payload.startTime, endTime: action.payload.endTime } : occ
        )
      };
    case 'UPDATE_ALL_STATUS':
      return {
        ...state,
        occurrences: state.occurrences.map(occ => ({ ...occ, status: action.payload })),
      };
    case 'START_EXPORT':
        return { ...state, processingState: 'exporting', processingProgress: 0 };
    case 'EXPORT_PROGRESS':
        return { ...state, processingProgress: action.payload };
    case 'EXPORT_COMPLETE':
        return { ...state, processingState: 'verifying', exportUrl: action.payload.exportUrl, processingProgress: 100 };
    case 'EXPORT_ERROR':
        return { ...state, processingState: 'error', errorMessage: action.payload };
    case 'CANCEL_VERIFICATION':
        if(state.exportUrl) URL.revokeObjectURL(state.exportUrl);
        return { ...state, processingState: 'idle', exportUrl: null };
    case 'SET_CURRENT_TIME':
      return { ...state, currentVideoTime: action.payload };
    case 'SET_IS_PLAYING':
      return { ...state, isPlaying: action.payload };
    case 'SET_TIMELINE_VIEW':
        return { ...state, timelineView: action.payload };
    default:
      return state;
  }
}

export const AppContext = createContext<{ state: AppState; dispatch: Dispatch<Action> }>({
  state: initialState,
  dispatch: () => null,
});

export default function App() {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const contextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);
  
  const videoRef = React.useRef<HTMLVideoElement>(null);
  const videoUrlRef = useRef<string | null>(state.videoUrl);
  const exportUrlRef = useRef<string | null>(state.exportUrl);

  useEffect(() => {
    if (videoUrlRef.current && videoUrlRef.current !== state.videoUrl) {
      URL.revokeObjectURL(videoUrlRef.current);
    }
    videoUrlRef.current = state.videoUrl;
  }, [state.videoUrl]);
  
  useEffect(() => {
    if (exportUrlRef.current && exportUrlRef.current !== state.exportUrl) {
      URL.revokeObjectURL(exportUrlRef.current);
    }
    exportUrlRef.current = state.exportUrl;
  }, [state.exportUrl]);


  useEffect(() => {
    return () => {
      if (videoUrlRef.current) URL.revokeObjectURL(videoUrlRef.current);
      if (exportUrlRef.current) URL.revokeObjectURL(exportUrlRef.current);
    };
  }, []);


  const handleTimeUpdate = () => {
    if (videoRef.current) {
      dispatch({ type: 'SET_CURRENT_TIME', payload: (videoRef.current as any).currentTime });
    }
  };
  
  const handlePlay = () => dispatch({ type: 'SET_IS_PLAYING', payload: true });
  const handlePause = () => dispatch({ type: 'SET_IS_PLAYING', payload: false });

  React.useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (state.isPlaying) {
      (video as any).play().catch(console.error);
    } else {
      (video as any).pause();
    }
  }, [state.isPlaying]);

  return (
    <AppContext.Provider value={contextValue}>
      <div className="bg-gray-900 text-gray-200 w-screen h-screen overflow-hidden flex flex-col font-sans">
        <Header />
         <main className="flex-grow grid grid-cols-12 grid-rows-1 gap-4 p-4 min-h-0 relative">
            { (state.processingState === 'detecting' || state.processingState === 'exporting' || state.processingState === 'error') && 
              <ProcessingOverlay />
            }
            
            {state.processingState === 'verifying' ? (
                <div className="col-span-12 h-full">
                    <VerificationScreen />
                </div>
            ) : state.videoUrl ? (
                <>
                  <div className="col-span-3 min-h-0">
                    <LeftSidebar />
                  </div>
                  <div className="col-span-6 flex flex-col gap-4 min-h-0">
                    <Timeline videoRef={videoRef} />
                  </div>
                  <div className="col-span-3 min-h-0">
                    <RightInspector videoRef={videoRef} />
                  </div>
                  <video 
                    ref={videoRef}
                    src={state.videoUrl} 
                    className="hidden" 
                    onTimeUpdate={handleTimeUpdate}
                    onPlay={handlePlay}
                    onPause={handlePause}
                    onLoadedMetadata={(e) => {
                        const target = e.target as HTMLVideoElement;
                        if(state.videoDuration !== (target as any).duration) { // Prevent re-setting on loops
                           dispatch({type: 'SET_VIDEO', payload: {file: state.videoFile!, url: state.videoUrl!, duration: (target as any).duration}});
                        }
                    }}
                  />
                </>
            ) : (
                <div className="col-span-12">
                  <VideoDropzone />
                </div>
            )}
        </main>
      </div>
    </AppContext.Provider>
  );
}