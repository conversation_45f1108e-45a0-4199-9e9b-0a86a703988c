

import React, { useContext, useMemo, useRef, useCallback } from 'react';
import { AppContext } from '../App';
import { WordOccurrence, DetectionStatus } from '../types';
import { PRE_ROLL_S } from '../constants';
import Icon from './Icon';
import Button from './Button';

const statusStyles: Record<DetectionStatus, string> = {
  pending: `bg-amber-500/70 border-amber-400`,
  accepted: `bg-green-500/70 border-green-400`,
  rejected: `bg-red-500/70 border-red-400`,
};

interface OccurrenceBlockProps {
  occurrence: WordOccurrence;
  isSelected: boolean;
  onClick: (e: React.MouseEvent) => void;
  onResize: (id: string, newStartTime: number, newEndTime: number) => void;
  viewStart: number;
  viewDuration: number;
}

const OccurrenceBlock: React.FC<OccurrenceBlockProps> = ({ occurrence, isSelected, onClick, onResize, viewStart, viewDuration }) => {
  // Don't render if it's completely out of view
  if (occurrence.endTime < viewStart || occurrence.startTime > viewStart + viewDuration) {
    return null;
  }
  
  const left = ((occurrence.startTime - viewStart) / viewDuration) * 100;
  const width = ((occurrence.endTime - occurrence.startTime) / viewDuration) * 100;

  const style = {
    left: `${left}%`,
    width: `${Math.max(width, 0.2)}%`, // min width for visibility
  };
  
  const handleResize = (e: React.MouseEvent<HTMLDivElement>, handle: 'left' | 'right') => {
    e.stopPropagation();
    const resizerElement = e.currentTarget;
    const timelineElement = (resizerElement as HTMLElement).parentElement?.parentElement;
    if (!timelineElement) return;

    const timelineRect = timelineElement.getBoundingClientRect();
    const initialX = e.clientX;
    const initialStartTime = occurrence.startTime;
    const initialEndTime = occurrence.endTime;

    const onMouseMove = (moveEvent: MouseEvent) => {
        const dx = (moveEvent as any).clientX - initialX;
        const dTime = (dx / timelineRect.width) * viewDuration;
        
        let newStartTime = initialStartTime;
        let newEndTime = initialEndTime;

        if (handle === 'left') {
            newStartTime = Math.max(viewStart, initialStartTime + dTime);
            if (newStartTime >= newEndTime) newStartTime = newEndTime - 0.1; // Prevent crossing over
        } else {
            newEndTime = Math.min(viewStart + viewDuration, initialEndTime + dTime);
            if (newEndTime <= newStartTime) newEndTime = newStartTime + 0.1; // Prevent crossing over
        }
        
        onResize(occurrence.id, newStartTime, newEndTime);
    };

    const onMouseUp = () => {
        (window as any).removeEventListener('mousemove', onMouseMove);
        (window as any).removeEventListener('mouseup', onMouseUp);
    };

    (window as any).addEventListener('mousemove', onMouseMove);
    (window as any).addEventListener('mouseup', onMouseUp);
  };

  return (
    <div
      className={`absolute top-0 h-full cursor-pointer group transition-all duration-200 ease-out ${statusStyles[occurrence.status]} ${isSelected ? 'ring-2 ring-white z-10' : 'z-0'}`}
      style={style}
      onClick={onClick}
    >
      {isSelected && (
        <>
          <div 
            className="absolute left-0 top-0 h-full w-2 cursor-col-resize z-20 flex items-center justify-start"
            onMouseDown={(e) => handleResize(e, 'left')}
          >
            <div className="bg-white w-0.5 h-1/2 rounded-full"></div>
          </div>
          <div 
            className="absolute right-0 top-0 h-full w-2 cursor-col-resize z-20 flex items-center justify-end"
            onMouseDown={(e) => handleResize(e, 'right')}
          >
            <div className="bg-white w-0.5 h-1/2 rounded-full"></div>
          </div>
        </>
      )}
      <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 bg-gray-900 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-30">
        {occurrence.word} @ {occurrence.startTime.toFixed(1)}s
      </div>
    </div>
  );
};

const Timeline: React.FC<{ videoRef: React.RefObject<HTMLVideoElement> }> = ({ videoRef }) => {
  const { state, dispatch } = useContext(AppContext);
  const timelineRef = useRef<HTMLDivElement>(null);
  const isPanning = useRef(false);

  const { start: viewStart, end: viewEnd } = state.timelineView;
  const viewDuration = viewEnd - viewStart;

  const formatTime = (timeInSeconds: number) => {
    if (isNaN(timeInSeconds) || timeInSeconds < 0) return '00:00';
    return new Date(timeInSeconds * 1000).toISOString().slice(14, 19);
  }

  const handleTimelineClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!timelineRef.current || !videoRef.current || (e.target as any).closest('.group')) return;
    
    const rect = (timelineRef.current as any).getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = viewStart + (viewDuration * percentage);
    
    (videoRef.current as any).currentTime = newTime;
    dispatch({ type: 'SET_CURRENT_TIME', payload: newTime });
  };
  
  const handleSelectOccurrence = (occurrence: WordOccurrence) => {
    dispatch({
      type: 'SELECT_OCCURRENCE',
      payload: { id: occurrence.id, time: Math.max(0, occurrence.startTime - PRE_ROLL_S) },
    });
  };

  const handleResizeOccurrence = useCallback((id: string, startTime: number, endTime: number) => {
    dispatch({ type: 'UPDATE_OCCURRENCE_TIME', payload: { id, startTime, endTime } });
  }, [dispatch]);

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (!timelineRef.current) return;

    const rect = (timelineRef.current as any).getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseTime = viewStart + (mouseX / rect.width) * viewDuration;
    const zoomFactor = 1.15;
    const newViewDuration = viewDuration * (e.deltaY < 0 ? 1 / zoomFactor : zoomFactor);
    const clampedDuration = Math.max(1, Math.min(newViewDuration, state.videoDuration));
    
    let newStart = mouseTime - (mouseX / rect.width) * clampedDuration;
    newStart = Math.max(0, Math.min(newStart, state.videoDuration - clampedDuration));
    const newEnd = newStart + clampedDuration;

    dispatch({ type: 'SET_TIMELINE_VIEW', payload: { start: newStart, end: newEnd } });
  };

  const handlePanStart = (e: React.MouseEvent) => {
    if ((e.target as any).closest('.group') || !timelineRef.current) return;
    
    isPanning.current = true;
    (timelineRef.current as any).style.cursor = 'grabbing';
    const startX = e.clientX;
    const initialViewStart = viewStart;

    const handlePanMove = (moveEvent: MouseEvent) => {
      if (!isPanning.current || !timelineRef.current) return;
      const dx = (moveEvent as any).clientX - startX;
      const dTime = (dx / (timelineRef.current as any).getBoundingClientRect().width) * viewDuration;
      let newStart = initialViewStart - dTime;
      newStart = Math.max(0, Math.min(newStart, state.videoDuration - viewDuration));
      dispatch({ type: 'SET_TIMELINE_VIEW', payload: { start: newStart, end: newStart + viewDuration } });
    };

    const handlePanEnd = () => {
      isPanning.current = false;
      if (timelineRef.current) (timelineRef.current as any).style.cursor = 'grab';
      (window as any).removeEventListener('mousemove', handlePanMove);
      (window as any).removeEventListener('mouseup', handlePanEnd);
    };

    (window as any).addEventListener('mousemove', handlePanMove);
    (window as any).addEventListener('mouseup', handlePanEnd);
  };

  const handleZoom = (direction: 'in' | 'out') => {
    const centerTime = viewStart + viewDuration / 2;
    const newViewDuration = viewDuration * (direction === 'in' ? 1 / 1.5 : 1.5);
    const clampedDuration = Math.max(1, Math.min(newViewDuration, state.videoDuration));
    let newStart = centerTime - clampedDuration / 2;
    newStart = Math.max(0, Math.min(newStart, state.videoDuration - clampedDuration));
    dispatch({ type: 'SET_TIMELINE_VIEW', payload: { start: newStart, end: newStart + clampedDuration } });
  };

  const playheadPosition = viewDuration > 0 ? ((state.currentVideoTime - viewStart) / viewDuration) * 100 : -100;
  
  const waveformGradient = useMemo(() => {
    const stops = Array.from({ length: 50 }, () => `rgba(255, 106, 0, ${Math.random() * 0.4 + 0.3}) ${Math.random() * 100}%`).join(', ');
    return `linear-gradient(90deg, ${stops})`;
  }, []);

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-white/10 rounded-xl p-4 flex flex-col h-full shadow-lg">
      <div className="flex-grow flex flex-col justify-center relative">
        <div className="text-xs text-gray-400 flex justify-between mb-2 font-mono">
            <span>{formatTime(viewStart)}</span>
            <span>{formatTime(viewEnd)}</span>
        </div>
        <div 
          ref={timelineRef} 
          className="relative w-full h-24 bg-gray-900/50 rounded-lg overflow-hidden cursor-grab" 
          onClick={handleTimelineClick}
          onWheel={handleWheel}
          onMouseDown={handlePanStart}
        >
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-full h-1/2" style={{ background: waveformGradient }}></div>
          </div>

          {state.occurrences.map((occ) => (
            <OccurrenceBlock
              key={occ.id}
              occurrence={occ}
              viewStart={viewStart}
              viewDuration={viewDuration}
              isSelected={state.selectedOccurrenceId === occ.id}
              onClick={(e) => { e.stopPropagation(); handleSelectOccurrence(occ); }}
              onResize={handleResizeOccurrence}
            />
          ))}
          
          {playheadPosition >= 0 && playheadPosition <= 100 && (
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-white z-20 pointer-events-none"
              style={{ left: `${playheadPosition}%` }}
            >
              <div className="absolute -top-1 -left-1.5 w-4 h-4 bg-white rounded-full border-2 border-gray-900"></div>
            </div>
          )}
        </div>

        <div className="flex justify-between items-center gap-6 mt-4">
            <div className="flex gap-1">
                <Button variant="ghost" className="!p-2 text-lg" onClick={() => handleZoom('out')}>-</Button>
                <Button variant="ghost" className="!p-2 text-lg" onClick={() => handleZoom('in')}>+</Button>
            </div>
            <div className="flex justify-center items-center gap-6">
                <Button variant="ghost" className="!p-2" onClick={() => (videoRef.current as any)!.currentTime -= 5}>
                    <Icon name="rewind" />
                </Button>
                <Button variant="ghost" className="!p-3" onClick={() => dispatch({type: 'SET_IS_PLAYING', payload: !state.isPlaying})}>
                    <Icon name={state.isPlaying ? 'pause' : 'play'} className="w-8 h-8" />
                </Button>
                <Button variant="ghost" className="!p-2" onClick={() => (videoRef.current as any)!.currentTime += 5}>
                    <Icon name="forward" />
                </Button>
            </div>
            <div className="flex gap-1 opacity-0 pointer-events-none">
                <Button variant="ghost" className="!p-2 text-lg">-</Button>
                <Button variant="ghost" className="!p-2 text-lg">+</Button>
            </div>
        </div>
      </div>
    </div>
  );
};

export default Timeline;