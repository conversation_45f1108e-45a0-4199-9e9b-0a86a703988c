

export type DetectionStatus = 'pending' | 'accepted' | 'rejected';
export type GeminiModelQuality = 'flash' | 'pro';

export interface WordOccurrence {
  id: string;
  word: string;
  startTime: number; // in seconds
  endTime: number; // in seconds
  status: DetectionStatus;
}

export interface AppState {
  videoFile: File | null;
  videoUrl: string | null;
  videoDuration: number;
  badWords: string;
  occurrences: WordOccurrence[];
  selectedOccurrenceId: string | null;
  currentVideoTime: number;
  isPlaying: boolean;
  processingState: 'idle' | 'detecting' | 'exporting' | 'error' | 'verifying';
  processingProgress: number;
  errorMessage: string | null;
  analysisQuality: GeminiModelQuality;
  exportUrl: string | null;
  timelineView: { start: number; end: number };
  hasJustUploaded: boolean;
}

export type Action =
  | { type: 'SET_VIDEO'; payload: { file: File; url: string; duration: number } }
  | { type: 'RESET_VIDEO' }
  | { type: 'SET_BAD_WORDS'; payload: string }
  | { type: 'SET_ANALYSIS_QUALITY'; payload: GeminiModelQuality }
  | { type: 'START_DETECTION' }
  | { type: 'DETECTION_PROGRESS'; payload: number }
  | { type: 'DETECTION_COMPLETE'; payload: WordOccurrence[] }
  | { type: 'DETECTION_ERROR'; payload: string }
  | { type: 'SELECT_OCCURRENCE'; payload: { id: string | null, time: number } }
  | { type: 'UPDATE_OCCURRENCE_STATUS'; payload: { id: string; status: DetectionStatus } }
  | { type: 'UPDATE_ALL_STATUS'; payload: DetectionStatus }
  | { type: 'START_EXPORT' }
  | { type: 'EXPORT_PROGRESS'; payload: number }
  | { type: 'EXPORT_COMPLETE'; payload: { exportUrl: string } }
  | { type: 'EXPORT_ERROR'; payload: string }
  | { type: 'CANCEL_VERIFICATION' }
  | { type: 'SET_CURRENT_TIME'; payload: number }
  | { type: 'SET_IS_PLAYING'; payload: boolean }
  | { type: 'SET_TIMELINE_VIEW'; payload: { start: number; end: number } }
  | { type: 'UPDATE_OCCURRENCE_TIME'; payload: { id: string; startTime: number; endTime: number } };