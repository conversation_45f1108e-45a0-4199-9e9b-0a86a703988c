

import React, { useContext, useMemo } from 'react';
import { AppContext } from '../App';
import Button from './Button';
import Icon from './Icon';
import { ACCENT_COLOR } from '../constants';
import { geminiService } from '../services/geminiService';

const Header: React.FC = () => {
    const { state, dispatch } = useContext(AppContext);
    const hasOccurrences = state.occurrences.length > 0;
    const hasVideo = !!state.videoFile;
    const hasAcceptedOccurrences = useMemo(() => state.occurrences.some(o => o.status === 'accepted'), [state.occurrences]);


    const handleApplyAll = () => {
        dispatch({ type: 'UPDATE_ALL_STATUS', payload: 'accepted' });
    };

    const handleRevertAll = () => {
        dispatch({ type: 'UPDATE_ALL_STATUS', payload: 'pending' });
    };

    const handleExport = async () => {
        dispatch({ type: 'START_EXPORT' });
        try {
            const acceptedOccurrences = state.occurrences.filter(o => o.status === 'accepted');
            
            const exportUrl = await geminiService.exportVideo(
                state.videoFile!,
                state.videoDuration,
                acceptedOccurrences,
                (progress) => dispatch({ type: 'EXPORT_PROGRESS', payload: progress })
            );

            dispatch({ type: 'EXPORT_COMPLETE', payload: { exportUrl } });
        } catch (error) {
            const message = error instanceof Error ? error.message : 'An unknown error occurred.';
            dispatch({ type: 'EXPORT_ERROR', payload: `Export failed: ${message}` });
        }
    };
    
    const handleReset = () => {
        if((window as any).confirm('Are you sure you want to load a new video? All current progress will be lost.')){
            dispatch({type: 'RESET_VIDEO'});
        }
    }

    const isActionDisabled = !hasVideo || (state.processingState !== 'idle' && state.processingState !== 'verifying');

    return (
        <header className="flex items-center justify-between p-4 bg-gray-900/50 backdrop-blur-sm border-b border-white/10 shadow-md">
            <h1 className="text-xl font-bold tracking-wider" style={{ color: ACCENT_COLOR }}>
                Intelli<span className="text-white">Mute</span>
            </h1>
            { state.processingState !== 'verifying' && (
                <div className="flex items-center gap-3">
                    <Button variant="ghost" onClick={handleReset} disabled={isActionDisabled}>
                        New Video
                    </Button>
                    <Button variant="secondary" onClick={handleApplyAll} disabled={!hasOccurrences || isActionDisabled} icon={<Icon name="checkAll" />}>
                        Apply All
                    </Button>
                    <Button variant="secondary" onClick={handleRevertAll} disabled={!hasOccurrences || isActionDisabled} icon={<Icon name="revert" />}>
                        Revert All
                    </Button>
                    <Button 
                        onClick={handleExport} 
                        disabled={!hasVideo || !hasAcceptedOccurrences || isActionDisabled} 
                        isLoading={state.processingState === 'exporting'}
                        icon={<Icon name="export" />}
                    >
                        Export Video
                    </Button>
                </div>
            )}
        </header>
    );
};

export default Header;