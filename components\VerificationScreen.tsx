

import React, { useContext, useMemo, useRef, useState } from 'react';
import { AppContext } from '../App';
import Button from './Button';
import Icon from './Icon';

const VerificationScreen: React.FC = () => {
    const { state, dispatch } = useContext(AppContext);
    const videoRef = useRef<HTMLVideoElement>(null);
    const [clipEndTime, setClipEndTime] = useState<number | null>(null);

    const acceptedOccurrences = useMemo(() => {
        return state.occurrences.filter(o => o.status === 'accepted');
    }, [state.occurrences]);

    const handleDownload = () => {
        if (!state.exportUrl || !state.videoFile) return;
        const a = (window as any).document.createElement('a');
        a.href = state.exportUrl;
        a.download = `IntelliMute_${state.videoFile.name}`;
        (window as any).document.body.appendChild(a);
        a.click();
        (window as any).document.body.removeChild(a);
    };

    const handleBackToEditor = () => {
        dispatch({ type: 'CANCEL_VERIFICATION' });
    };
    
    const playClip = (startTime: number, endTime: number) => {
        const video = videoRef.current;
        if (!video) return;

        const clipStart = Math.max(0, startTime - 5);
        const clipEnd = Math.min(state.videoDuration, endTime + 5);
        
        (video as any).currentTime = clipStart;
        setClipEndTime(clipEnd);
        (video as any).play();
    };
    
    const handleTimeUpdate = () => {
        const video = videoRef.current;
        if (!video || !clipEndTime) return;

        if ((video as any).currentTime >= clipEndTime) {
            (video as any).pause();
            setClipEndTime(null);
        }
    };

    if (!state.exportUrl) {
        return (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
                <Icon name="warning" className="w-12 h-12 mb-4 text-red-500" />
                <h2 className="text-2xl font-bold">Error</h2>
                <p>No export URL found. Please try exporting again.</p>
                <Button onClick={handleBackToEditor} className="mt-4">Back to Editor</Button>
            </div>
        );
    }

    return (
        <div className="grid grid-cols-12 gap-4 h-full">
            {/* Main Video Player Column */}
            <div className="col-span-8 flex flex-col bg-gray-800/50 backdrop-blur-md border border-white/10 rounded-xl shadow-lg p-4">
                <h2 className="text-xl font-semibold mb-4">Verification</h2>
                <div className="flex-grow bg-black rounded-lg aspect-video">
                    <video
                        ref={videoRef}
                        src={state.exportUrl}
                        controls
                        className="w-full h-full"
                        onTimeUpdate={handleTimeUpdate}
                    />
                </div>
                <div className="flex items-center justify-end gap-4 pt-4 mt-auto border-t border-white/10">
                    <Button variant="secondary" onClick={handleBackToEditor}>
                        Back to Editor
                    </Button>
                    <Button variant="primary" onClick={handleDownload} icon={<Icon name="export" />}>
                        Download Video
                    </Button>
                </div>
            </div>

            {/* Muted Clips Column */}
            <div className="col-span-4 flex flex-col bg-gray-800/50 backdrop-blur-md border border-white/10 rounded-xl shadow-lg">
                <h3 className="text-lg font-semibold p-4 border-b border-white/10">Muted Clips ({acceptedOccurrences.length})</h3>
                <div className="overflow-y-auto flex-grow">
                    {acceptedOccurrences.map(occ => (
                        <div key={occ.id} className="p-3 border-b border-white/10 flex items-center justify-between gap-4">
                            <div>
                                <p className="font-semibold text-orange-400">{occ.word}</p>
                                <p className="text-sm text-gray-400 font-mono">{occ.startTime.toFixed(2)}s - {occ.endTime.toFixed(2)}s</p>
                            </div>
                            <Button 
                                variant="ghost" 
                                className="!px-2 !py-1"
                                onClick={() => playClip(occ.startTime, occ.endTime)}
                                title="Play clip with 5s buffer"
                            >
                                <Icon name="play" />
                            </Button>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default VerificationScreen;