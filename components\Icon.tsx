import React from 'react';

type IconName = 'upload' | 'check' | 'x' | 'play' | 'pause' | 'rewind' | 'forward' | 'settings' | 'export' | 'revert' | 'checkAll' | 'warning' | 'volumeMute';

export interface IconProps {
  name: IconName;
  className?: string;
}

const ICONS: Record<IconName, React.ReactNode> = {
  upload: <path strokeLinecap="round" strokeLinejoin="round" d="M12 16.5V9.75m0 0l-3.75 3.75M12 9.75l3.75 3.75M12 19.5a7.5 7.5 0 100-15 7.5 7.5 0 000 15z" />,
  check: <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />,
  x: <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />,
  play: <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.972l-11.54 6.347a1.125 1.125 0 01-1.667-.986V5.653z" />,
  pause: <path strokeLinecap="round" strokeLinejoin="round"d="M15.75 5.25v13.5m-6-13.5v13.5" />,
  rewind: <path strokeLinecap="round" strokeLinejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />,
  forward: <path strokeLinecap="round" strokeLinejoin="round" d="M15 15l6-6m0 0l-6-6m6 6H9a6 6 0 000 12h3" />,
  settings: <path strokeLinecap="round" strokeLinejoin="round" d="M10.343 3.94c.09-.542.56-1.007 1.11-1.227l.126-.053c.57-.238 1.232-.238 1.802 0l.126.053c.55.22 1.02.685 1.11 1.227l.068.411c.26.495.595.942.984 1.328l.298.297c.414.413.92.748 1.455.966l.41.163c.563.224 1.007.695 1.228 1.256l.052.126c.238.57.238 1.232 0 1.802l-.052.126c-.22.56-.695 1.007-1.256 1.228l-.41.162c-.535.218-1.04.553-1.455.966l-.298.297c-.39.386-.725.833-.984 1.328l-.068.411c-.09.542-.56 1.007-1.11 1.227l-.126.053c-.57.238-1.232-.238-1.802 0l-.126-.053c-.55-.22-1.02-.685-1.11-1.227l-.068-.411a6.532 6.532 0 01-.984-1.328l-.298-.297a6.532 6.532 0 01-1.455-.966l-.41-.163c-.563-.224-1.007-.695-1.228-1.256l-.052-.126c-.238-.57-.238-1.232 0-1.802l.052.126c.22-.56.695-1.007 1.256-1.228l.41-.162a6.532 6.532 0 011.455-.966l.298.297c.39-.386.725.833.984-1.328l.068-.411zM12 15a3 3 0 100-6 3 3 0 000 6z" />,
  export: <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />,
  revert: <path strokeLinecap="round" strokeLinejoin="round" d="M9 15L3 9m0 0l6-6M3 9h13.5a3.75 3.75 0 010 7.5H15" />,
  checkAll: <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />,
  warning: <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />,
  volumeMute: <path strokeLinecap="round" strokeLinejoin="round" d="M11 5L6 9H2v6h4l5 5V5z M17 9l6 6 M23 9l-6 6" />,
};

const Icon: React.FC<IconProps> = ({ name, className = 'w-6 h-6' }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
      {ICONS[name]}
    </svg>
  );
};

export default Icon;