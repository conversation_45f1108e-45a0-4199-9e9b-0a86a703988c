import React from 'react';
import Icon, { IconProps } from './Icon';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'success' | 'danger';
  icon?: React.ReactElement<IconProps>;
  iconPosition?: 'left' | 'right';
  isLoading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  icon,
  iconPosition = 'left',
  isLoading = false,
  className,
  ...props
}) => {
  const baseClasses = 'flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed';

  const variantClasses = {
    primary: 'bg-orange-600 text-white hover:bg-orange-500 focus:ring-orange-500',
    secondary: 'bg-gray-700 text-gray-200 hover:bg-gray-600 focus:ring-gray-500',
    ghost: 'bg-transparent text-gray-300 hover:bg-gray-700 hover:text-white focus:ring-gray-500',
    success: 'bg-green-600 text-white hover:bg-green-500 focus:ring-green-500',
    danger: 'bg-red-600 text-white hover:bg-red-500 focus:ring-red-500',
  };

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading && (
        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
      )}
      {!isLoading && icon && iconPosition === 'left' && React.cloneElement(icon, { className: 'w-5 h-5' })}
      {!isLoading && children}
      {!isLoading && icon && iconPosition === 'right' && React.cloneElement(icon, { className: 'w-5 h-5' })}
    </button>
  );
};

export default Button;
