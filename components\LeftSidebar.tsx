

import React, { useContext } from 'react';
import { AppContext } from '../App';
import Button from './Button';
import Icon from './Icon';
import { geminiService } from '../services/geminiService';
import { GeminiModelQuality } from '../types';

const LeftSidebar: React.FC = () => {
  const { state, dispatch } = useContext(AppContext);

  const handleDetect = async () => {
    dispatch({ type: 'START_DETECTION' });
    try {
      const occurrences = await geminiService.detectWords(
        state.videoFile!,
        state.videoDuration,
        state.badWords,
        (progress) => dispatch({ type: 'DETECTION_PROGRESS', payload: progress }),
        state.analysisQuality
      );
      dispatch({ type: 'DETECTION_COMPLETE', payload: occurrences });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An unknown error occurred.';
      dispatch({ type: 'DETECTION_ERROR', payload: `Detection failed: ${message}` });
    }
  };

  const setQuality = (quality: GeminiModelQuality) => {
    dispatch({ type: 'SET_ANALYSIS_QUALITY', payload: quality });
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-white/10 rounded-xl p-4 flex flex-col h-full shadow-lg">
      <div className="flex items-center gap-3 mb-4 border-b border-white/10 pb-2">
        <Icon name="volumeMute" className="w-6 h-6 text-orange-400" />
        <h2 className="text-xl font-bold tracking-tight text-gray-100">Words to Mute</h2>
      </div>
      <p className="text-sm text-gray-400 mb-2">Enter words or phrases to mute, separated by commas.</p>
      <textarea
        value={state.badWords}
        onChange={(e) => dispatch({ type: 'SET_BAD_WORDS', payload: (e.target as any).value })}
        className="w-full flex-grow bg-gray-900/50 border border-white/20 rounded-lg p-2 text-gray-200 focus:ring-2 focus:ring-orange-500 focus:outline-none resize-none mb-4"
        placeholder="e.g., heck, darn, fudge"
      />
      <div className="border-t border-white/10 pt-4 mt-auto">
        <h3 className="font-semibold mb-3 flex items-center gap-2"><Icon name="settings" className="w-5 h-5 text-orange-500" /> Detection Settings</h3>
        
        <div className="mb-4">
            <label className="text-sm font-medium text-gray-300 block mb-2">Analysis Quality</label>
            <div className="flex gap-1 rounded-lg bg-gray-900/50 p-1">
                <Button
                    variant={state.analysisQuality === 'flash' ? 'secondary' : 'ghost'}
                    onClick={() => setQuality('flash')}
                    className="w-full !py-1 !text-xs"
                    title="Fastest analysis, good for previews."
                >
                    Flash (Fastest)
                </Button>
                <Button
                    variant={state.analysisQuality === 'pro' ? 'secondary' : 'ghost'}
                    onClick={() => setQuality('pro')}
                    className="w-full !py-1 !text-xs"
                    title="Slower, more thorough analysis for best results."
                >
                    Pro (Highest Quality)
                </Button>
            </div>
        </div>

        <div className="space-y-3 text-sm">
            <label className="flex items-center justify-between cursor-pointer">
                <span>Phonetic Matching</span>
                <div className="relative">
                    <input type="checkbox" className="sr-only peer" defaultChecked/>
                    <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                </div>
            </label>
             <label className="flex items-center justify-between cursor-not-allowed opacity-50">
                <span>Case-Insensitive</span>
                <div className="relative">
                    <input type="checkbox" className="sr-only peer" defaultChecked disabled/>
                    <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                </div>
            </label>
        </div>
         <Button
            onClick={handleDetect}
            disabled={!state.videoFile || !state.badWords || state.processingState === 'detecting'}
            isLoading={state.processingState === 'detecting'}
            className={`w-full mt-6 ${state.hasJustUploaded ? 'animate-pulse' : ''}`}
        >
            Detect Words
        </Button>
      </div>
    </div>
  );
};

export default LeftSidebar;