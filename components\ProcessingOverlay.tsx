
import React, { useContext } from 'react';
import { AppContext } from '../App';
import Icon from './Icon';
import Button from './Button';

const ProcessingOverlay: React.FC = () => {
  const { state, dispatch } = useContext(AppContext);

  const title = state.processingState === 'detecting' ? 'Analyzing Audio...' : state.processingState === 'exporting' ? 'Exporting Video...' : 'An Error Occurred';
  const description = state.processingState === 'detecting' ? 'Please wait while we detect words in your video. This may take a few minutes.' : state.processingState === 'exporting' ? 'Applying mutes and packaging your final video file.' : state.errorMessage;

  if (state.processingState === 'idle') return null;
  
  const isExportError = state.errorMessage?.startsWith('Export');

  return (
    <div className="absolute inset-0 bg-gray-900/80 backdrop-blur-sm flex flex-col items-center justify-center z-50 p-8">
        <div className="bg-gray-800 border border-white/10 rounded-xl p-8 max-w-lg w-full text-center shadow-2xl">
            {state.processingState === 'error' ? (
                <Icon name="warning" className="w-16 h-16 text-red-500 mx-auto mb-4" />
            ) : (
                <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
            )}
            
            <h2 className="text-3xl font-bold mb-3">{title}</h2>
            <p className="text-gray-400 mb-6">{description}</p>

            {state.processingState !== 'error' && (
                <div className="w-full bg-gray-700 rounded-full h-2.5">
                    <div className="bg-orange-600 h-2.5 rounded-full transition-all duration-300" style={{ width: `${state.processingProgress}%` }}></div>
                </div>
            )}
             {state.processingState === 'error' && (
                isExportError ? (
                    <Button onClick={() => dispatch({type: 'CANCEL_VERIFICATION'})} variant='secondary'>Close</Button>
                ) : (
                    <Button onClick={() => dispatch({type: 'RESET_VIDEO'})} variant='danger'>Start Over</Button>
                )
             )}
        </div>
    </div>
  );
};

export default ProcessingOverlay;