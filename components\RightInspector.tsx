
import React, { useContext, useEffect, useRef, useState, useCallback } from 'react';
import { AppContext } from '../App';
import { PRE_ROLL_S, POST_ROLL_S, ACCEPTED_COLOR, REJECTED_COLOR } from '../constants';
import Button from './Button';
import Icon from './Icon';

const RightInspector: React.FC<{ videoRef: React.RefObject<HTMLVideoElement> }> = ({ videoRef }) => {
  const { state, dispatch } = useContext(AppContext);
  const selectedOccurrence = state.occurrences.find(occ => occ.id === state.selectedOccurrenceId);
  
  const previewVideoRef = useRef<HTMLVideoElement>(null);
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);
  const [previewTime, setPreviewTime] = useState(0);
  const isInitialPlay = useRef(false); // Ref to handle initial play on selection

  const clipStartTime = selectedOccurrence ? Math.max(0, selectedOccurrence.startTime - PRE_ROLL_S) : 0;
  const clipEndTime = selectedOccurrence ? Math.min(state.videoDuration, selectedOccurrence.endTime + POST_ROLL_S) : 0;

  // Effect to set up the video when a new occurrence is selected
  useEffect(() => {
    const video = previewVideoRef.current;
    if (video && selectedOccurrence) {
      // Instead of playing directly, we signal that we want an initial play.
      // The onLoadedData handler will perform the seek and play.
      isInitialPlay.current = true;
      // Calling load() is crucial to ensure onLoadedData fires, even if the src is the same.
      (video as any).load();
    } else if (!selectedOccurrence) {
      setIsPreviewPlaying(false);
    }
    // Main video player should jump to the start of the pre-roll
    if (videoRef.current) {
        (videoRef.current as any).currentTime = state.currentVideoTime;
    }
  }, [selectedOccurrence, videoRef, state.currentVideoTime]);

  // Effect to control play/pause on the preview video
  useEffect(() => {
    const video = previewVideoRef.current;
    if (!video) return;
    if (isPreviewPlaying) {
      (video as any).play().catch(console.error);
    } else {
      (video as any).pause();
    }
  }, [isPreviewPlaying]);
  
  const handlePreviewTimeUpdate = () => {
    const video = previewVideoRef.current;
    if (!video) return;

    setPreviewTime((video as any).currentTime);

    // Loop the preview clip
    if ((video as any).currentTime >= clipEndTime) {
      (video as any).currentTime = clipStartTime;
      // Keep playing if it was playing
      if(!isPreviewPlaying) {
          (video as any).pause();
      }
    }
  };
  
  const handlePreviewLoadedData = () => {
    const video = previewVideoRef.current;
    // When the video data is loaded, check if we intended to play it.
    if (video && isInitialPlay.current) {
        isInitialPlay.current = false; // Consume the flag to prevent re-playing
        (video as any).currentTime = clipStartTime;
        setPreviewTime(clipStartTime);
        setIsPreviewPlaying(true);
    }
  };

  const selectNextPending = useCallback(() => {
      const currentIndex = state.occurrences.findIndex(o => o.id === state.selectedOccurrenceId);
      const nextPendingIndex = state.occurrences.findIndex((o, i) => i > currentIndex && o.status === 'pending');
      if(nextPendingIndex !== -1){
        const nextOcc = state.occurrences[nextPendingIndex];
        dispatch({ type: 'SELECT_OCCURRENCE', payload: { id: nextOcc.id, time: Math.max(0, nextOcc.startTime - PRE_ROLL_S) } });
      } else {
        setIsPreviewPlaying(false);
      }
  }, [dispatch, state.occurrences, state.selectedOccurrenceId]);

  const handleAccept = useCallback(() => {
    if (selectedOccurrence) {
      dispatch({ type: 'UPDATE_OCCURRENCE_STATUS', payload: { id: selectedOccurrence.id, status: 'accepted' } });
      selectNextPending();
    }
  }, [dispatch, selectedOccurrence, selectNextPending]);

  const handleReject = useCallback(() => {
    if (selectedOccurrence) {
      dispatch({ type: 'UPDATE_OCCURRENCE_STATUS', payload: { id: selectedOccurrence.id, status: 'rejected' } });
      selectNextPending();
    }
  }, [dispatch, selectedOccurrence, selectNextPending]);
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedOccurrence || ((e.target as any).tagName === 'INPUT' || (e.target as any).tagName === 'TEXTAREA')) return;
      if ((e as any).key.toLowerCase() === 'a') {
        e.preventDefault();
        handleAccept();
      } else if ((e as any).key.toLowerCase() === 'r') {
        e.preventDefault();
        handleReject();
      }
    };
    (window as any).addEventListener('keydown', handleKeyDown);
    return () => (window as any).removeEventListener('keydown', handleKeyDown);
  }, [handleAccept, handleReject, selectedOccurrence]);

  const togglePlay = () => {
      const video = previewVideoRef.current;
      if (!video) return;
      if((video as any).currentTime >= clipEndTime) {
         (video as any).currentTime = clipStartTime;
      }
      setIsPreviewPlaying(prev => !prev);
  }

  const handleRewind = () => {
     const video = previewVideoRef.current;
     if(video) (video as any).currentTime = clipStartTime;
  }
  
  if (!selectedOccurrence) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-md border border-white/10 rounded-xl p-4 flex flex-col justify-center items-center h-full shadow-lg text-gray-400">
        <p>Select a detection on the timeline</p>
        <p className="text-sm">to preview and take action.</p>
      </div>
    );
  }

  const statusColor = selectedOccurrence.status === 'accepted' ? ACCEPTED_COLOR : selectedOccurrence.status === 'rejected' ? REJECTED_COLOR : 'transparent';
  const progressPercentage = clipEndTime > clipStartTime ? ((previewTime - clipStartTime) / (clipEndTime - clipStartTime)) * 100 : 0;

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-white/10 rounded-xl flex flex-col h-full shadow-lg overflow-hidden transition-all duration-300" style={{borderColor: statusColor}}>
      <div className="p-4 border-b" style={{borderColor: statusColor}}>
        <h2 className="text-lg font-semibold">Clip Inspector</h2>
        <p className="text-sm text-gray-400">Word Found: <span className="font-bold text-orange-400">{selectedOccurrence.word}</span></p>
        <p className="text-sm text-gray-400">Time: <span className="font-mono">{selectedOccurrence.startTime.toFixed(2)}s</span></p>
      </div>

      <div className="flex-grow p-4 flex flex-col">
        <div className="aspect-video bg-black rounded-lg mb-4 overflow-hidden relative group">
          <video 
            ref={previewVideoRef} 
            src={state.videoUrl ?? ''} 
            className="w-full h-full object-contain" 
            onTimeUpdate={handlePreviewTimeUpdate} 
            onClick={togglePlay}
            onLoadedData={handlePreviewLoadedData}
          />
           <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
               <div className='bg-black/50 rounded-full p-2'>
                <Icon name={isPreviewPlaying ? 'pause' : 'play'} className="w-10 h-10 text-white" />
               </div>
           </div>
        </div>

        <div className="w-full bg-gray-900/50 rounded-full h-1.5 mb-4">
            <div className="bg-orange-500 h-1.5 rounded-full" style={{ width: `${progressPercentage}%` }}></div>
        </div>

        <div className="flex justify-center items-center gap-4 mb-4">
            <Button onClick={handleRewind} variant='ghost' className="!p-2"><Icon name='rewind' className="w-6 h-6" /></Button>
            <Button onClick={togglePlay} variant='ghost' className="!p-3">
                <Icon name={isPreviewPlaying ? 'pause' : 'play'} className="w-8 h-8" />
            </Button>
             <div className="w-6 h-6"></div>
        </div>


        <div className="mt-auto grid grid-cols-2 gap-4">
          <Button variant="danger" onClick={handleReject} icon={<Icon name="x"/>}>
            Reject (R)
          </Button>
          <Button variant="success" onClick={handleAccept} icon={<Icon name="check"/>}>
            Accept (A)
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RightInspector;
