

import React, { useContext, useState, useCallback, useRef, useEffect } from 'react';
import { AppContext } from '../App';
import Icon from './Icon';
import Button from './Button';
import { useToast } from './ToastContainer';

const VideoDropzone: React.FC = () => {
  const { dispatch } = useContext(AppContext);
  const { showSuccess, showError } = useToast();
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoElementRef = useRef<HTMLVideoElement | null>(null);
  const urlRef = useRef<string | null>(null);

  // Cleanup function to handle video element and URL cleanup
  const cleanup = useCallback(() => {
    if (videoElementRef.current) {
      try {
        if (videoElementRef.current.parentNode) {
          videoElementRef.current.parentNode.removeChild(videoElementRef.current);
        }
      } catch (e) {
        console.warn('Error removing video element:', e);
      }
      videoElementRef.current = null;
    }
    if (urlRef.current) {
      URL.revokeObjectURL(urlRef.current);
      urlRef.current = null;
    }
    setIsProcessing(false);
  }, []);

  // Cleanup on component unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const handleFile = useCallback((file: File | null | undefined) => {
    // Clear any previous error
    setError(null);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    if (!file) {
      return;
    }

    // Validate file type
    const validTypes = ['video/mp4', 'video/quicktime', 'video/x-matroska', 'video/avi', 'video/webm'];
    const isValidType = file.type.startsWith('video/') || validTypes.includes(file.type);

    if (!isValidType) {
      const errorMsg = 'Please select a valid video file (MP4, MOV, MKV, AVI, WebM).';
      setError(errorMsg);
      showError(errorMsg);
      return;
    }

    // Check file size (optional - adjust limit as needed)
    const maxSize = 2 * 1024 * 1024 * 1024; // 2GB
    if (file.size > maxSize) {
      const errorMsg = 'File size is too large. Please select a file smaller than 2GB.';
      setError(errorMsg);
      showError(errorMsg);
      return;
    }

    setIsProcessing(true);

    try {
      const url = URL.createObjectURL(file);
      urlRef.current = url;

      // Create a video element to read metadata
      const video = document.createElement('video');
      videoElementRef.current = video;
      video.preload = 'metadata';
      video.style.display = 'none';
      video.style.position = 'absolute';
      video.style.top = '-9999px';

      const handleLoadedMetadata = () => {
        try {
          console.log('Video metadata loaded:', { duration: video.duration, file: file.name });
          if (video.duration && isFinite(video.duration)) {
            console.log('Dispatching SET_VIDEO action');
            dispatch({ type: 'SET_VIDEO', payload: { file, url, duration: video.duration } });
            // Don't cleanup here - let App.tsx handle the URL
            if (videoElementRef.current) {
              try {
                if (videoElementRef.current.parentNode) {
                  videoElementRef.current.parentNode.removeChild(videoElementRef.current);
                }
              } catch (e) {
                console.warn('Error removing video element:', e);
              }
              videoElementRef.current = null;
            }
            setIsProcessing(false);
            console.log('Video upload completed successfully');
            showSuccess(`Video "${file.name}" uploaded successfully!`);
          } else {
            throw new Error('Invalid video duration');
          }
        } catch (error) {
          console.error('Error processing video metadata:', error);
          const errorMsg = 'Could not read video metadata. The file may be corrupt or in an unsupported format.';
          setError(errorMsg);
          showError(errorMsg);
          cleanup();
        }
      };

      const handleError = () => {
        console.error('Video loading error');
        const errorMsg = 'Could not load video metadata. The file may be corrupt or in an unsupported format.';
        setError(errorMsg);
        showError(errorMsg);
        cleanup();
      };

      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('error', handleError);

      // Append to body to ensure it's in the DOM
      document.body.appendChild(video);
      video.src = url;

    } catch (error) {
      console.error('Error creating object URL:', error);
      const errorMsg = 'Failed to process the selected file. Please try again.';
      setError(errorMsg);
      showError(errorMsg);
      cleanup();
    }
  }, [dispatch, cleanup]);

  const onDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);
  
  const onDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const onDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const onDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    handleFile((e.dataTransfer as any).files?.[0]);
  }, [handleFile]);

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFile(e.currentTarget.files?.[0]);
  };

  const onButtonClick = () => {
    if (!isProcessing) {
      fileInputRef.current?.click();
    }
  };

  const clearError = () => {
    setError(null);
  };

  return (
    <div
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      onDragOver={onDragOver}
      onDrop={onDrop}
      className={`w-full h-full flex flex-col items-center justify-center p-8 rounded-2xl border-2 border-dashed transition-colors duration-300 ${
        isDragging ? 'border-orange-500 bg-gray-700/50' :
        error ? 'border-red-500' : 'border-gray-600'
      }`}
    >
        <div className="text-center p-12 bg-gray-800/50 backdrop-blur-md rounded-2xl border border-white/10">
            {isProcessing ? (
                <>
                    <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <h2 className="text-2xl font-semibold mb-2">Processing Video...</h2>
                    <p className="text-gray-400 mb-6">Reading video metadata, please wait</p>
                </>
            ) : (
                <>
                    <Icon name="upload" className={`w-16 h-16 mx-auto mb-4 transition-transform duration-300 ${
                        isDragging ? 'scale-110 text-orange-500' :
                        error ? 'text-red-500' : 'text-gray-400'
                    }`} />
                    <h2 className="text-2xl font-semibold mb-2">Drop Your Video Here</h2>
                    <p className="text-gray-400 mb-6">Supports MP4, MOV, MKV, AVI, WebM files</p>

                    {error && (
                        <div className="mb-6 p-4 bg-red-900/50 border border-red-500 rounded-lg">
                            <p className="text-red-300 text-sm">{error}</p>
                            <Button onClick={clearError} variant="secondary" className="mt-2 text-xs">
                                Dismiss
                            </Button>
                        </div>
                    )}

                    <input
                        type="file"
                        ref={fileInputRef}
                        onChange={onFileChange}
                        accept="video/mp4,video/quicktime,video/x-matroska,video/avi,video/webm,.mov,.mkv,.avi,.webm"
                        className="hidden"
                        disabled={isProcessing}
                    />
                    <Button
                        onClick={onButtonClick}
                        variant="primary"
                        disabled={isProcessing}
                    >
                        Or Click to Select File
                    </Button>
                </>
            )}
        </div>
    </div>
  );
};

export default VideoDropzone;