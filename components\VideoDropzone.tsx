

import React, { useContext, useState, useCallback } from 'react';
import { AppContext } from '../App';
import Icon from './Icon';
import Button from './Button';

const VideoDropzone: React.FC = () => {
  const { dispatch } = useContext(AppContext);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFile = useCallback((file: File | null | undefined) => {
    if (file && (file.type.startsWith('video/') || ['video/mp4', 'video/quicktime', 'video/x-matroska'].includes(file.type))) {
      const url = URL.createObjectURL(file);
      // Create a video element to read metadata
      const video = (window as any).document.createElement('video');
      video.preload = 'metadata';
      video.style.display = 'none'; // Keep it hidden

      const cleanup = () => {
        // Remove the temporary element from the DOM
        (window as any).document.body.removeChild(video);
      };

      video.onloadedmetadata = () => {
        dispatch({ type: 'SET_VIDEO', payload: { file, url, duration: video.duration } });
        // The URL is passed to the state and should be revoked when no longer needed (handled in App.tsx)
        cleanup();
      };
      
      video.onerror = () => {
        (window as any).alert('Could not load video metadata. The file may be corrupt or in an unsupported format.');
        URL.revokeObjectURL(url); // On error, the URL is not used, so it's safe to revoke.
        cleanup();
      };

      // Append to body to ensure it's in the DOM, which can help with event firing reliability
      (window as any).document.body.appendChild(video);
      video.src = url; // Set src after appending and adding listeners

    } else {
      if(file){ // Only alert if a file was actually selected but was of the wrong type
          (window as any).alert('Please select a valid video file (MP4, MOV, MKV).');
      }
    }
  }, [dispatch]);

  const onDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);
  
  const onDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const onDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const onDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    handleFile((e.dataTransfer as any).files?.[0]);
  }, [handleFile]);

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFile(e.currentTarget.files?.[0]);
  };
  
  const onButtonClick = () => {
      (fileInputRef.current as any)?.click();
  }

  return (
    <div
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      onDragOver={onDragOver}
      onDrop={onDrop}
      className={`w-full h-full flex flex-col items-center justify-center p-8 rounded-2xl border-2 border-dashed transition-colors duration-300 ${isDragging ? 'border-orange-500 bg-gray-700/50' : 'border-gray-600'}`}
    >
        <div className="text-center p-12 bg-gray-800/50 backdrop-blur-md rounded-2xl border border-white/10">
            <Icon name="upload" className={`w-16 h-16 mx-auto mb-4 transition-transform duration-300 ${isDragging ? 'scale-110 text-orange-500' : 'text-gray-400'}`} />
            <h2 className="text-2xl font-semibold mb-2">Drop Your Video Here</h2>
            <p className="text-gray-400 mb-6">Supports MP4, MOV, MKV files</p>
            <input
                type="file"
                ref={fileInputRef}
                onChange={onFileChange}
                accept="video/mp4,video/quicktime,video/x-matroska,.mov,.mkv"
                className="hidden"
            />
            <Button onClick={onButtonClick} variant="primary">
                Or Click to Select File
            </Button>
        </div>
    </div>
  );
};

export default VideoDropzone;